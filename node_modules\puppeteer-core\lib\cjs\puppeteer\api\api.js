"use strict";
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
__exportStar(require("./Browser.js"), exports);
__exportStar(require("./BrowserContext.js"), exports);
__exportStar(require("./CDPSession.js"), exports);
__exportStar(require("./Dialog.js"), exports);
__exportStar(require("./ElementHandle.js"), exports);
__exportStar(require("./Environment.js"), exports);
__exportStar(require("./Frame.js"), exports);
__exportStar(require("./HTTPRequest.js"), exports);
__exportStar(require("./HTTPResponse.js"), exports);
__exportStar(require("./Input.js"), exports);
__exportStar(require("./JSHandle.js"), exports);
__exportStar(require("./Page.js"), exports);
__exportStar(require("./Realm.js"), exports);
__exportStar(require("./Target.js"), exports);
__exportStar(require("./WebWorker.js"), exports);
__exportStar(require("./locators/locators.js"), exports);
//# sourceMappingURL=api.js.map