#!/usr/bin/env python3
"""
Исправленный парсер CPA Quebec Directory
Основан на реальном анализе структуры сайта
С автоматическим решением капчи через Anti-Captcha API
"""
import asyncio
import json
import logging
import os
import sys
import re
import time
import random
import math
from datetime import datetime
from pathlib import Path
import argparse

import aiohttp
from dotenv import load_dotenv
from playwright.async_api import async_playwright, Page, BrowserContext

# Загружаем переменные окружения
load_dotenv()

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger("quebec_parser")

# Константы
BASE_URL = "https://cpaquebec.ca/en/find-a-cpa/cpa-directory/"
OUTPUT_DIR = Path("output")
ANTICAPTCHA_KEY = os.getenv("ANTICAPTCHA_API_KEY")

# Глобальная сессия для Anti-Captcha
anticaptcha_session = None

# Глобальный множитель задержек для управления скоростью
DELAY_MULTIPLIER = 1.0

# === ФУНКЦИИ ЭМУЛЯЦИИ ЧЕЛОВЕЧЕСКОГО ПОВЕДЕНИЯ ===

async def human_delay(min_ms: int = 500, max_ms: int = 2000, reason: str = ""):
    """Человеческая задержка с логированием и учетом глобального множителя"""
    global DELAY_MULTIPLIER
    delay = random.uniform(min_ms, max_ms) / 1000 * DELAY_MULTIPLIER
    if reason:
        logger.debug(f"Человеческая пауза {delay:.2f}с ({reason})")
    else:
        logger.debug(f"Человеческая пауза {delay:.2f}с")
    await asyncio.sleep(delay)

async def human_mouse_movement(page: Page, target_selector: str = None):
    """Эмулирует естественное движение мыши"""
    try:
        # Получаем размеры viewport
        viewport = page.viewport_size
        if not viewport:
            viewport = {"width": 1280, "height": 800}

        # Генерируем случайные промежуточные точки
        start_x = random.randint(100, viewport["width"] - 100)
        start_y = random.randint(100, viewport["height"] - 100)

        if target_selector:
            try:
                # Если указан целевой элемент, двигаемся к нему
                element = page.locator(target_selector).first
                if await element.count() > 0:
                    box = await element.bounding_box()
                    if box:
                        end_x = box["x"] + box["width"] / 2
                        end_y = box["y"] + box["height"] / 2
                    else:
                        end_x = random.randint(100, viewport["width"] - 100)
                        end_y = random.randint(100, viewport["height"] - 100)
                else:
                    end_x = random.randint(100, viewport["width"] - 100)
                    end_y = random.randint(100, viewport["height"] - 100)
            except:
                end_x = random.randint(100, viewport["width"] - 100)
                end_y = random.randint(100, viewport["height"] - 100)
        else:
            end_x = random.randint(100, viewport["width"] - 100)
            end_y = random.randint(100, viewport["height"] - 100)

        # Создаем плавную траекторию движения
        steps = random.randint(5, 15)
        for i in range(steps):
            progress = i / (steps - 1)
            # Добавляем небольшую кривизну к траектории
            curve_offset = math.sin(progress * math.pi) * random.randint(-20, 20)

            current_x = start_x + (end_x - start_x) * progress + curve_offset
            current_y = start_y + (end_y - start_y) * progress + curve_offset

            await page.mouse.move(current_x, current_y)
            await asyncio.sleep(random.uniform(0.01, 0.05))

        logger.debug(f"Движение мыши: ({start_x}, {start_y}) -> ({end_x}, {end_y})")

    except Exception as e:
        logger.debug(f"Ошибка при движении мыши: {e}")

async def human_scroll(page: Page, direction: str = "down", amount: int = None):
    """Эмулирует естественную прокрутку страницы"""
    try:
        if amount is None:
            amount = random.randint(200, 800)

        if direction == "down":
            delta_y = amount
        else:
            delta_y = -amount

        # Прокручиваем небольшими порциями для естественности
        steps = random.randint(3, 8)
        step_size = delta_y / steps

        for i in range(steps):
            await page.mouse.wheel(0, step_size)
            await asyncio.sleep(random.uniform(0.05, 0.15))

        logger.debug(f"Прокрутка {direction} на {amount}px")

    except Exception as e:
        logger.debug(f"Ошибка при прокрутке: {e}")

async def human_click(page: Page, selector: str, delay_before: bool = True, delay_after: bool = True):
    """Эмулирует человеческий клик с движением мыши и задержками"""
    try:
        if delay_before:
            await human_delay(300, 1500, "перед кликом")

        # Движение мыши к элементу
        await human_mouse_movement(page, selector)
        await human_delay(100, 500, "наведение на элемент")

        # Клик
        element = page.locator(selector).first
        if await element.count() > 0:
            await element.click()
            logger.debug(f"Человеческий клик по: {selector}")
        else:
            logger.warning(f"Элемент не найден для клика: {selector}")
            return False

        if delay_after:
            await human_delay(200, 1000, "после клика")

        return True

    except Exception as e:
        logger.error(f"Ошибка при человеческом клике по {selector}: {e}")
        return False

async def human_type(page: Page, selector: str, text: str, clear_first: bool = True):
    """Эмулирует человеческий ввод текста с естественными задержками"""
    try:
        element = page.locator(selector).first
        if await element.count() == 0:
            logger.warning(f"Элемент не найден для ввода: {selector}")
            return False

        # Фокусируемся на элементе
        await element.focus()
        await human_delay(200, 500, "фокус на поле")

        if clear_first:
            await element.clear()
            await human_delay(100, 300, "очистка поля")

        # Вводим текст посимвольно с естественными задержками
        for char in text:
            await element.type(char)
            # Варьируем скорость ввода
            if char == ' ':
                delay = random.uniform(0.1, 0.3)  # Пробелы вводим быстрее
            elif char.isupper():
                delay = random.uniform(0.15, 0.4)  # Заглавные буквы медленнее
            else:
                delay = random.uniform(0.05, 0.25)  # Обычные символы

            await asyncio.sleep(delay)

        logger.debug(f"Человеческий ввод в {selector}: {text}")
        await human_delay(200, 600, "после ввода")

        return True

    except Exception as e:
        logger.error(f"Ошибка при человеческом вводе в {selector}: {e}")
        return False

async def simulate_reading_page(page: Page, min_time: int = 3000, max_time: int = 8000):
    """Симулирует чтение страницы с прокруткой и движениями мыши"""
    try:
        reading_time = random.randint(min_time, max_time) / 1000
        logger.debug(f"Симуляция чтения страницы {reading_time:.1f}с")

        start_time = time.time()

        while time.time() - start_time < reading_time:
            action = random.choice(["scroll", "mouse_move", "pause"])

            if action == "scroll":
                direction = random.choice(["down", "down", "down", "up"])  # Больше вероятность прокрутки вниз
                await human_scroll(page, direction, random.randint(100, 400))
            elif action == "mouse_move":
                await human_mouse_movement(page)
            else:
                await human_delay(500, 2000, "пауза при чтении")

    except Exception as e:
        logger.debug(f"Ошибка при симуляции чтения: {e}")

async def setup_stealth_browser(context: BrowserContext):
    """Настраивает браузер для обхода детекции автоматизации"""
    try:
        # Добавляем стелс-скрипты
        await context.add_init_script("""
            // Переопределяем webdriver property
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });

            // Переопределяем plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });

            // Переопределяем languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });

            // Переопределяем chrome property
            window.chrome = {
                runtime: {},
            };

            // Переопределяем permissions
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );

            // Добавляем случайные свойства для имитации реального браузера
            Object.defineProperty(navigator, 'hardwareConcurrency', {
                get: () => 4,
            });

            Object.defineProperty(navigator, 'deviceMemory', {
                get: () => 8,
            });
        """)

        logger.debug("Стелс-настройки браузера применены")

    except Exception as e:
        logger.error(f"Ошибка при настройке стелс-браузера: {e}")

# Категории клиентов с их правильными селекторами
CLIENT_CATEGORIES = {
    "Individuals": "ListeClienteleDesserviesLeftColumn_0__Selected",
    "Large companies": "ListeClienteleDesserviesLeftColumn_1__Selected",
    "NFPOs": "ListeClienteleDesserviesLeftColumn_2__Selected",
    "Professional firms": "ListeClienteleDesserviesLeftColumn_3__Selected",
    "Public corporations": "ListeClienteleDesserviesLeftColumn_4__Selected",
    "Retailers": "ListeClienteleDesserviesRightColumn_0__Selected",
    "Self-employed workers": "ListeClienteleDesserviesRightColumn_1__Selected",
    "SMEs": "ListeClienteleDesserviesRightColumn_2__Selected",
    "Start-ups": "ListeClienteleDesserviesRightColumn_3__Selected",
    "Syndicates of co-owners": "ListeClienteleDesserviesRightColumn_4__Selected"
}

async def get_anticaptcha_session():
    """Получает или создает сессию для Anti-Captcha API"""
    global anticaptcha_session
    if anticaptcha_session is None or anticaptcha_session.closed:
        anticaptcha_session = aiohttp.ClientSession()
    return anticaptcha_session

async def close_anticaptcha_session():
    """Закрывает сессию Anti-Captcha"""
    global anticaptcha_session
    if anticaptcha_session and not anticaptcha_session.closed:
        await anticaptcha_session.close()
        anticaptcha_session = None

async def anticaptcha_request(method: str, **payload):
    """Выполняет запрос к Anti-Captcha API"""
    url = f"https://api.anti-captcha.com/{method}"
    session = await get_anticaptcha_session()

    if "clientKey" not in payload and ANTICAPTCHA_KEY:
        payload["clientKey"] = ANTICAPTCHA_KEY

    try:
        async with session.post(url, json=payload, timeout=30) as resp:
            resp.raise_for_status()
            response_json = await resp.json()
            logger.debug(f"Anti-Captcha {method}: {response_json}")
            return response_json
    except Exception as e:
        logger.error(f"Ошибка Anti-Captcha API ({method}): {e}")
        return {"errorId": 1, "errorDescription": str(e)}

async def create_captcha_task(site_key: str, page_url: str):
    """Создает задачу решения капчи в Anti-Captcha"""
    if not ANTICAPTCHA_KEY:
        logger.error("ANTICAPTCHA_API_KEY не установлен")
        return False, None

    logger.info(f"Создание задачи Anti-Captcha, sitekey={site_key[:8]}...")

    task_payload = {
        "task": {
            "type": "NoCaptchaTaskProxyless",
            "websiteURL": page_url,
            "websiteKey": site_key,
        },
        "softId": 8041
    }

    response = await anticaptcha_request("createTask", **task_payload)

    task_id = response.get("taskId")
    error_id = response.get("errorId", 0)

    if error_id > 0 or not task_id:
        error_code = response.get("errorCode", "UNKNOWN_ERROR")
        error_desc = response.get("errorDescription", "Не удалось создать задачу")
        logger.error(f"Ошибка Anti-Captcha: {error_code} - {error_desc}")
        return False, None

    logger.info(f"Задача Anti-Captcha создана, ID: {task_id}")
    return True, task_id

async def get_captcha_result(task_id: int, max_attempts: int = 60):
    """Получает результат решения капчи"""
    if not ANTICAPTCHA_KEY:
        return False, None

    logger.info(f"Ожидание результата Anti-Captcha для задачи {task_id}...")

    await asyncio.sleep(5)  # Начальная задержка

    for attempt in range(max_attempts):
        response = await anticaptcha_request("getTaskResult", taskId=task_id)

        status = response.get("status")
        error_id = response.get("errorId", 0)

        if error_id > 0:
            error_code = response.get("errorCode", "UNKNOWN_ERROR")
            error_desc = response.get("errorDescription", "Ошибка получения результата")
            logger.error(f"Ошибка Anti-Captcha: {error_code} - {error_desc}")
            return False, None

        if status == "ready":
            solution = response.get("solution")
            token = solution.get("gRecaptchaResponse") if solution else None
            if token and len(token) > 50:
                logger.info(f"Капча решена! Длина токена: {len(token)}")
                return True, token
            else:
                logger.error("Anti-Captcha вернул пустой токен")
                return False, None
        elif status == "processing":
            logger.debug(f"Задача {task_id} обрабатывается... (попытка {attempt + 1})")
        else:
            logger.warning(f"Неизвестный статус: {status}")

        await asyncio.sleep(3)

    logger.error(f"Превышено время ожидания для задачи {task_id}")
    return False, None

async def extract_recaptcha_sitekey(page: Page):
    """Извлекает sitekey из reCAPTCHA"""
    try:
        # Ищем в div.g-recaptcha
        sitekey = await page.evaluate("""
            () => {
                const recaptchaDiv = document.querySelector('.g-recaptcha');
                if (recaptchaDiv && recaptchaDiv.getAttribute('data-sitekey')) {
                    return recaptchaDiv.getAttribute('data-sitekey');
                }

                // Ищем в iframe
                const iframes = document.querySelectorAll('iframe[src*="recaptcha"]');
                for (const iframe of iframes) {
                    const src = iframe.src;
                    const match = src.match(/k=([^&]+)/);
                    if (match) {
                        return match[1];
                    }
                }

                // Ищем в любом элементе с data-sitekey
                const element = document.querySelector('[data-sitekey]');
                if (element) {
                    return element.getAttribute('data-sitekey');
                }

                return null;
            }
        """)

        if sitekey:
            logger.info(f"Найден sitekey: {sitekey[:10]}...")
            return True, sitekey
        else:
            logger.error("Sitekey не найден")
            return False, None
    except Exception as e:
        logger.error(f"Ошибка при извлечении sitekey: {e}")
        return False, None

async def insert_captcha_token(page: Page, token: str):
    """Вставляет токен решения капчи"""
    try:
        await page.evaluate(f"""
            (token) => {{
                // Находим или создаем поле g-recaptcha-response
                let response = document.querySelector('#g-recaptcha-response');
                if (!response) {{
                    response = document.createElement('textarea');
                    response.id = 'g-recaptcha-response';
                    response.name = 'g-recaptcha-response';
                    response.className = 'g-recaptcha-response';
                    response.style.display = 'none';
                    document.body.appendChild(response);
                }}

                // Вставляем токен
                response.value = token;
                response.dispatchEvent(new Event('change', {{ bubbles: true }}));

                // Вызываем callback если есть
                if (window.grecaptcha && window.___grecaptcha_cfg) {{
                    const widgets = window.___grecaptcha_cfg.clients;
                    for (const clientId in widgets) {{
                        const client = widgets[clientId];
                        if (client.callback) {{
                            client.callback(token);
                        }}
                    }}
                }}

                console.log('Токен капчи вставлен:', token.substring(0, 20) + '...');
            }}
        """, token)

        logger.info("Токен капчи успешно вставлен")
        return True
    except Exception as e:
        logger.error(f"Ошибка при вставке токена: {e}")
        return False

async def close_cookies_banner(page: Page) -> bool:
    """Закрывает баннер с cookies если он есть"""
    try:
        cookie_banner = page.locator("text=Tout refuser")
        if await cookie_banner.is_visible(timeout=5000):
            await cookie_banner.click()
            logger.info("Cookie banner закрыт")
            await page.wait_for_timeout(2000)
            return True
    except:
        pass
    logger.debug("Cookie banner не найден или уже закрыт")
    return False

async def wait_for_captcha_manual(page: Page) -> bool:
    """Ожидает ручного решения капчи"""
    logger.info("ВНИМАНИЕ: Обнаружена reCAPTCHA!")
    logger.info("Пожалуйста, решите капчу вручную в браузере и нажмите Enter для продолжения...")

    # Проверяем, есть ли уже решенная капча
    token = await page.evaluate("""
        () => {
            const response = document.querySelector('#g-recaptcha-response');
            return response && response.value && response.value.length > 50;
        }
    """)

    if not token:
        # Ждем ручного ввода
        input("Нажмите Enter после решения капчи: ")

        # Проверяем еще раз
        token = await page.evaluate("""
            () => {
                const response = document.querySelector('#g-recaptcha-response');
                return response && response.value && response.value.length > 50;
            }
        """)

    if token:
        logger.info("Капча решена!")
        return True
    else:
        logger.warning("Капча не решена")
        return False

async def solve_captcha_auto(page: Page) -> bool:
    """Автоматически решает капчу через Anti-Captcha с эмуляцией человеческого поведения"""
    if not ANTICAPTCHA_KEY:
        logger.warning("ANTICAPTCHA_API_KEY не установлен, используем ручное решение")
        return await wait_for_captcha_manual(page)

    # Симулируем изучение страницы перед взаимодействием с капчей
    logger.info("Изучаем страницу перед решением капчи...")
    await simulate_reading_page(page, 2000, 5000)

    # Сначала кликаем по галочке "я не робот" с человеческим поведением
    logger.info("Кликаем по галочке 'я не робот' с эмуляцией человека...")
    await click_recaptcha_checkbox_human(page)

    # Ждем с человеческими задержками и проверяем, может капча решилась автоматически
    await human_delay(2000, 4000, "ожидание после клика по капче")
    if await is_recaptcha_solved(page):
        logger.info("Капча решена автоматически после клика!")
        return True

    # Если не решилась автоматически, используем Anti-Captcha
    logger.info("Капча не решена автоматически, используем Anti-Captcha...")

    # Извлекаем sitekey
    success, site_key = await extract_recaptcha_sitekey(page)
    if not success or not site_key:
        logger.error("Не удалось извлечь sitekey")
        return False

    # Создаем задачу
    success, task_id = await create_captcha_task(site_key, BASE_URL)
    if not success or not task_id:
        logger.error("Не удалось создать задачу Anti-Captcha")
        return False

    # Симулируем ожидание пользователя во время решения капчи
    logger.info("Симулируем человеческое поведение во время решения капчи...")
    await simulate_waiting_behavior(page)

    # Получаем результат
    success, token = await get_captcha_result(task_id)
    if not success or not token:
        logger.error("Не удалось получить токен от Anti-Captcha")
        return False

    # Вставляем токен с задержкой
    await human_delay(1000, 2000, "перед вставкой токена")
    if not await insert_captcha_token(page, token):
        logger.error("Не удалось вставить токен")
        return False

    # Проверяем, что капча решена с человеческой задержкой
    await human_delay(1500, 3000, "проверка решения капчи")
    if await is_recaptcha_solved(page):
        logger.info("Капча автоматически решена!")
        return True
    else:
        logger.warning("Токен вставлен, но капча не считается решенной")
        # Пробуем еще раз кликнуть по галочке с человеческим поведением
        await click_recaptcha_checkbox_human(page)
        await human_delay(2000, 4000, "повторная проверка капчи")
        return await is_recaptcha_solved(page)

async def click_recaptcha_checkbox_human(page: Page) -> bool:
    """Кликает по галочке 'я не робот' в reCAPTCHA с эмуляцией человеческого поведения"""
    try:
        # Ищем iframe с reCAPTCHA
        recaptcha_iframe = page.locator("iframe[src*='api2/anchor']").first
        if await recaptcha_iframe.count() == 0:
            logger.debug("reCAPTCHA iframe не найден")
            return False

        # Симулируем движение мыши к области капчи
        await human_mouse_movement(page, "iframe[src*='api2/anchor']")
        await human_delay(500, 1500, "изучение капчи")

        # Получаем frame
        frame = await recaptcha_iframe.content_frame()
        if not frame:
            logger.debug("Не удалось получить frame reCAPTCHA")
            return False

        # Ищем чекбокс в frame
        checkbox = frame.locator("#recaptcha-anchor")
        if await checkbox.count() == 0:
            logger.debug("Чекбокс reCAPTCHA не найден")
            return False

        # Человеческая задержка перед кликом
        await human_delay(800, 2000, "размышление перед кликом по капче")

        # Кликаем по чекбоксу
        await checkbox.click()
        logger.info("Человеческий клик по галочке 'я не робот' выполнен")

        # Человеческая задержка после клика
        await human_delay(1000, 2500, "ожидание после клика по капче")
        return True

    except Exception as e:
        logger.error(f"Ошибка при человеческом клике по галочке reCAPTCHA: {e}")
        return False

async def simulate_waiting_behavior(page: Page):
    """Симулирует поведение пользователя во время ожидания решения капчи"""
    try:
        # Случайные действия во время ожидания
        actions = ["mouse_move", "scroll", "pause", "mouse_move"]

        for _ in range(random.randint(3, 8)):
            action = random.choice(actions)

            if action == "mouse_move":
                await human_mouse_movement(page)
            elif action == "scroll":
                await human_scroll(page, random.choice(["down", "up"]), random.randint(50, 200))
            else:
                await human_delay(1000, 3000, "пауза во время ожидания")

            await human_delay(500, 1500, "между действиями ожидания")

    except Exception as e:
        logger.debug(f"Ошибка при симуляции ожидания: {e}")

async def handle_captcha(page: Page) -> bool:
    """Обрабатывает капчу"""
    # Проверяем наличие капчи
    captcha_exists = await page.evaluate("""
        () => {
            return document.querySelector('.g-recaptcha') !== null ||
                   document.querySelector('iframe[src*="recaptcha"]') !== null;
        }
    """)

    if not captcha_exists:
        logger.info("Капча не найдена")
        return True

    # Пробуем автоматическое решение
    return await solve_captcha_auto(page)

async def select_multiple_categories(page: Page, categories: list) -> bool:
    """Выбирает несколько категорий клиентов с эмуляцией человеческого поведения"""
    logger.info(f"Выбираем категории с человеческим поведением: {', '.join(categories)}")

    # Симулируем изучение формы перед выбором
    await simulate_reading_page(page, 1500, 3000)

    # Сначала снимаем все чекбоксы с человеческими задержками
    logger.debug("Сбрасываем все чекбоксы категорий...")
    await page.evaluate("""
        // Снимаем все чекбоксы категорий
        const checkboxes = document.querySelectorAll('input[type="checkbox"][name*="ListeClienteleDesservies"]');
        checkboxes.forEach(checkbox => {
            if (checkbox.checked) {
                checkbox.checked = false;
                checkbox.dispatchEvent(new Event('change', { bubbles: true }));
            }
        });
    """)

    await human_delay(500, 1000, "после сброса чекбоксов")

    success_count = 0

    # Устанавливаем нужные чекбоксы с человеческим поведением
    for i, category in enumerate(categories):
        if category not in CLIENT_CATEGORIES:
            logger.error(f"Неизвестная категория: {category}")
            continue

        checkbox_id = CLIENT_CATEGORIES[category]
        logger.info(f"Выбираем категорию: {category} (ID: {checkbox_id})")

        try:
            checkbox = page.locator(f"#{checkbox_id}")

            # Прокручиваем к элементу с человеческим поведением
            await checkbox.scroll_into_view_if_needed()
            await human_delay(300, 800, f"прокрутка к категории {category}")

            # Движение мыши к чекбоксу
            await human_mouse_movement(page, f"#{checkbox_id}")
            await human_delay(400, 1200, f"изучение категории {category}")

            # Человеческий клик по чекбоксу
            await checkbox.check(force=True)

            # Проверяем, что чекбокс отмечен
            is_checked = await checkbox.is_checked()
            if is_checked:
                logger.info(f"Категория '{category}' успешно выбрана")
                success_count += 1
            else:
                logger.error(f"Не удалось выбрать категорию '{category}'")

            # Пауза между выбором категорий (кроме последней)
            if i < len(categories) - 1:
                await human_delay(800, 2000, f"пауза после выбора {category}")

        except Exception as e:
            logger.error(f"Ошибка при выборе категории '{category}': {e}")

    # Финальная пауза для "обдумывания" выбора
    await human_delay(1000, 2500, "обдумывание выбранных категорий")

    logger.info(f"Успешно выбрано {success_count} из {len(categories)} категорий")
    return success_count > 0

async def process_multiple_categories(page: Page, categories: list = None) -> list:
    """Обрабатывает поиск с несколькими категориями с эмуляцией человеческого поведения"""
    if not categories:
        # По умолчанию выбираем несколько популярных категорий
        categories = ["Individuals", "SMEs", "Self-employed workers"]

    logger.info(f"=== Обработка категорий с человеческим поведением: {', '.join(categories)} ===")

    # Переходим на страницу поиска с человеческой задержкой
    logger.info("Переходим на страницу поиска...")
    await page.goto(BASE_URL, timeout=60000)
    await page.wait_for_load_state("domcontentloaded")

    # Симулируем первоначальное изучение страницы
    await simulate_reading_page(page, 3000, 6000)

    # Закрываем cookie banner с человеческим поведением
    await close_cookies_banner_human(page)

    # Выбираем несколько категорий с человеческим поведением
    if not await select_multiple_categories(page, categories):
        logger.error("Не удалось выбрать ни одной категории")
        return []

    # Обрабатываем капчу с человеческим поведением
    if not await handle_captcha(page):
        logger.error("Не удалось решить капчу")
        return []

    # Нажимаем кнопку поиска с человеческим поведением
    if not await click_search_button(page):
        logger.error("Не удалось нажать кнопку поиска")
        return []

    # Ждем перехода на страницу результатов с человеческими задержками
    logger.info("Ожидаем загрузку результатов...")
    await human_delay(3000, 6000, "ожидание результатов поиска")

    # Симулируем изучение страницы результатов
    await simulate_reading_page(page, 2000, 4000)

    # Извлекаем результаты
    results = await extract_results(page)

    # Добавляем категории к каждому результату
    for result in results:
        result["categories"] = categories

    logger.info(f"Найдено {len(results)} результатов для категорий: {', '.join(categories)}")
    return results

async def close_cookies_banner_human(page: Page) -> bool:
    """Закрывает баннер с cookies с эмуляцией человеческого поведения"""
    try:
        cookie_banner = page.locator("text=Tout refuser")
        if await cookie_banner.is_visible(timeout=5000):
            logger.info("Обнаружен cookie banner, закрываем с человеческим поведением...")

            # Симулируем чтение баннера
            await human_delay(1000, 2500, "чтение cookie banner")

            # Движение мыши к кнопке
            await human_mouse_movement(page, "text=Tout refuser")
            await human_delay(300, 800, "наведение на кнопку отказа")

            # Клик
            await cookie_banner.click()
            logger.info("Cookie banner закрыт")

            await human_delay(1000, 2000, "после закрытия banner")
            return True
    except:
        pass
    logger.debug("Cookie banner не найден или уже закрыт")
    return False

async def wait_for_captcha_window_to_close(page: Page, timeout: int = 30000) -> bool:
    """Ожидает исчезновения окна капчи"""
    try:
        logger.info("Ожидаем исчезновения окна капчи...")

        # Ждем исчезновения iframe с капчей
        await page.wait_for_function("""
            () => {
                // Проверяем отсутствие iframe с капчей
                const captchaIframes = document.querySelectorAll('iframe[src*="api2/bframe"], iframe[src*="recaptcha"]');
                const visibleIframes = Array.from(captchaIframes).filter(iframe => {
                    const style = window.getComputedStyle(iframe);
                    return style.display !== 'none' && style.visibility !== 'hidden' && iframe.offsetHeight > 0;
                });

                // Проверяем отсутствие overlay элементов
                const overlays = document.querySelectorAll('[style*="z-index"], .recaptcha-overlay, .captcha-overlay');
                const visibleOverlays = Array.from(overlays).filter(overlay => {
                    const style = window.getComputedStyle(overlay);
                    const zIndex = parseInt(style.zIndex) || 0;
                    return zIndex > 1000 && style.display !== 'none' && style.visibility !== 'hidden';
                });

                return visibleIframes.length === 0 && visibleOverlays.length === 0;
            }
        """, timeout=timeout)

        logger.info("Окно капчи исчезло")
        return True

    except Exception as e:
        logger.warning(f"Таймаут ожидания исчезновения окна капчи: {e}")
        return False

async def click_search_button(page: Page) -> bool:
    """Кликает кнопку поиска с эмуляцией человеческого поведения"""
    try:
        logger.info("Подготовка к нажатию кнопки поиска...")

        # Проверяем, что окно капчи точно исчезло
        logger.info("Финальная проверка отсутствия окна капчи...")
        captcha_window_closed = await wait_for_captcha_window_to_close(page)
        if not captcha_window_closed:
            logger.warning("Окно капчи все еще видимо, но продолжаем...")

        # Симулируем финальную проверку формы
        await simulate_reading_page(page, 1000, 2500)

        # Ищем кнопку поиска
        search_selectors = [
            'form#FindACPABottinForm button[type="submit"]',
            'button:has-text("SEARCH")',
            'button[type="submit"]',
            'input[type="submit"]'
        ]

        search_button = None
        used_selector = None

        for selector in search_selectors:
            button = page.locator(selector).first
            if await button.count() > 0:
                search_button = button
                used_selector = selector
                logger.debug(f"Найдена кнопка поиска: {selector}")
                break

        if not search_button:
            logger.warning("Кнопка поиска не найдена, пробуем JavaScript")
            # Fallback через JavaScript
            result = await page.evaluate("""
                () => {
                    // Ищем форму поиска CPA
                    const form = document.querySelector('#FindACPABottinForm');
                    if (form) {
                        const button = form.querySelector('button[type="submit"]');
                        if (button) {
                            button.click();
                            console.log('Clicked search button via JS (form)');
                            return true;
                        }
                    }

                    // Ищем любую кнопку поиска
                    const buttons = document.querySelectorAll('button[type="submit"]');
                    for (const btn of buttons) {
                        if (btn.textContent && btn.textContent.toLowerCase().includes('search')) {
                            btn.click();
                            console.log('Clicked search button via JS (any)');
                            return true;
                        }
                    }

                    // Если не найдена, отправляем первую форму
                    const forms = document.querySelectorAll('form');
                    if (forms.length > 0) {
                        forms[0].submit();
                        console.log('Submitted first form via JS');
                        return true;
                    }

                    return false;
                }
            """)

            if result:
                logger.info("Кнопка поиска нажата (через JavaScript)")
                await human_delay(1000, 2000, "после нажатия кнопки поиска")
                return True
            else:
                logger.error("Кнопка поиска не найдена")
                return False

        # Человеческое взаимодействие с кнопкой
        logger.info(f"Нажимаем кнопку поиска с человеческим поведением: {used_selector}")

        # Прокручиваем к кнопке
        await search_button.scroll_into_view_if_needed()
        await human_delay(300, 800, "прокрутка к кнопке поиска")

        # Движение мыши к кнопке
        await human_mouse_movement(page, used_selector)
        await human_delay(500, 1500, "наведение на кнопку поиска")

        # Финальная пауза перед кликом (имитация размышления)
        await human_delay(800, 2000, "размышление перед поиском")

        # Клик по кнопке
        await search_button.click(force=True, timeout=10000)
        logger.info("Человеческий клик по кнопке поиска выполнен")

        # Пауза после клика
        await human_delay(1000, 2500, "ожидание после нажатия поиска")

        return True

    except Exception as e:
        logger.error(f"Ошибка при человеческом нажатии кнопки поиска: {e}")
        return False

async def extract_results(page: Page) -> list:
    """Извлекает результаты поиска"""
    results = []

    # Ждем загрузки результатов
    try:
        await page.wait_for_selector(".search-results, .results, .listing", timeout=30000)
    except:
        logger.warning("Селектор результатов не найден, продолжаем")

    # Пробуем различные селекторы для результатов профилей CPA
    result_selectors = [
        ".search-result",
        ".result-item",
        ".listing-item",
        ".cpa-listing",
        "article",
        ".card",
        # Специфичные селекторы для профилей CPA
        "div.row:has(.cpa-name)",
        "div.row:has(a[href*='/profile/'])",
        "div.row:has(a[href*='/directory/'])",
        "div.row div.col:has(h3)",
        "div.row div.col:has(h2)",
        "div.row div.column:has(h3)",
        "div.row div.column:has(h2)",
        # Ищем div, содержащие имена и контактную информацию
        "div:has(strong):has(a[href*='mailto'])",
        "div:has(h3):has(a[href*='tel'])",
        "div.profile-card",
        "div.member-card",
        "tr:has(td)",  # Возможно результаты в таблице
        "li.profile",
        "li.member"
    ]

    cards = None
    count = 0

    # Сначала проверяем, действительно ли мы на странице результатов
    current_url = page.url
    page_content = await page.content()

    # Ищем индикаторы того, что это страница результатов поиска
    has_results_text = await page.evaluate("""
        () => {
            const text = document.body.innerText.toLowerCase();
            return text.includes('results') ||
                   text.includes('found') ||
                   text.includes('search') ||
                   text.includes('directory') ||
                   text.includes('listing');
        }
    """)

    logger.debug(f"URL страницы: {current_url}")
    logger.debug(f"Страница содержит текст результатов: {has_results_text}")

    # Ищем результаты с помощью JavaScript
    js_analysis = await page.evaluate("""
        () => {
            // Ищем элементы, которые могут содержать профили CPA
            const possibleResults = [];

            // Метод 1: Поиск элементов с email
            const emailElements = Array.from(document.querySelectorAll('a[href^="mailto"]'));
            emailElements.forEach(elem => {
                const parent = elem.closest('div, tr, li, article');
                if (parent && !possibleResults.includes(parent)) {
                    possibleResults.push(parent);
                }
            });

            // Метод 2: Поиск элементов с телефоном
            const phoneElements = Array.from(document.querySelectorAll('a[href^="tel"]'));
            phoneElements.forEach(elem => {
                const parent = elem.closest('div, tr, li, article');
                if (parent && !possibleResults.includes(parent)) {
                    possibleResults.push(parent);
                }
            });

            // Метод 3: Поиск текста, содержащего CPA
            const textElements = Array.from(document.querySelectorAll('*'));
            textElements.forEach(elem => {
                const text = elem.textContent || '';
                if (text.includes('CPA') &&
                    (text.includes('@') || text.includes('tel:') || /\(\d{3}\)/.test(text))) {
                    const parent = elem.closest('div, tr, li, article');
                    if (parent && !possibleResults.includes(parent)) {
                        possibleResults.push(parent);
                    }
                }
            });

            return {
                emailElementsCount: emailElements.length,
                phoneElementsCount: phoneElements.length,
                possibleResultsCount: possibleResults.length,
                pageText: document.body.innerText.substring(0, 500)
            };
        }
    """)

    logger.debug(f"JS анализ: {js_analysis}")

    for selector in result_selectors:
        try:
            cards = page.locator(selector)
            count = await cards.count()
            if count > 0:
                logger.debug(f"Пробуем селектор: {selector}, найдено элементов: {count}")

                # Проверяем первый элемент на наличие контактной информации
                if count > 0:
                    first_card = cards.nth(0)
                    first_card_text = ""
                    try:
                        first_card_text = await first_card.inner_text()
                    except:
                        pass

                    # Проверяем, содержит ли элемент контактную информацию
                    has_contact_info = (
                        '@' in first_card_text or
                        'tel:' in first_card_text or
                        'CPA' in first_card_text or
                        re.search(r'\(\d{3}\)', first_card_text)
                    )

                    if has_contact_info:
                        logger.info(f"Найдены результаты с контактной информацией с селектором: {selector}")
                        break
                    else:
                        logger.debug(f"Элементы с селектором {selector} не содержат контактной информации")
                        cards = None
                        count = 0
        except Exception as e:
            logger.debug(f"Ошибка с селектором {selector}: {e}")
            continue

    if not cards or count == 0:
        logger.warning("Результаты поиска с контактной информацией не найдены")
        # Попробуем найти хотя бы элементы с именами
        try:
            cards = page.locator("div:has(h3), div:has(h2), div:has(strong)")
            count = await cards.count()
            if count > 0:
                logger.info(f"Найдено {count} элементов с заголовками как резервный вариант")
        except:
            pass

    if not cards or count == 0:
        logger.warning("Вообще никаких результатов не найдено")
        return results

    logger.info(f"Обрабатываем {count} результатов...")

    for i in range(min(count, 50)):  # Ограничиваем до 50 результатов для безопасности
        try:
            card = cards.nth(i)

            # Получаем весь текст карточки
            card_text = ""
            try:
                card_text = await card.inner_text()
            except:
                pass

            # Пропускаем элементы навигации и меню
            if (card_text and (
                'Menu' in card_text[:50] or
                'General public' in card_text[:100] or
                'Back' in card_text[:50] or
                'Find a CPA' in card_text[:100] or
                'SEARCH' in card_text[:100] or
                'Home Se' in card_text[:100]
            )):
                logger.debug(f"Пропускаем элемент навигации #{i+1}")
                continue

            # Извлекаем имя
            name = "Unknown"
            name_selectors = ["h3", "h2", "h4", ".name", ".title", "strong", "b"]
            for name_sel in name_selectors:
                try:
                    name_elem = card.locator(name_sel).first
                    if await name_elem.count() > 0:
                        name_text = await name_elem.inner_text()
                        name_text = name_text.strip()
                        # Проверяем, что это не системный текст
                        if (name_text and
                            len(name_text) > 2 and
                            not any(word in name_text.lower() for word in ['menu', 'back', 'search', 'home', 'general public'])):
                            name = name_text
                            break
                except:
                    continue

            # Извлекаем ссылку на профиль
            profile_url = None
            try:
                # Ищем ссылки, которые могут вести к профилям
                link_selectors = [
                    "a[href*='/profile/']",
                    "a[href*='/directory/']",
                    "a[href*='/cpa/']",
                    "a[href*='/member/']"
                ]

                for link_sel in link_selectors:
                    link_elem = card.locator(link_sel).first
                    if await link_elem.count() > 0:
                        href = await link_elem.get_attribute("href")
                        if href:
                            if href.startswith("/"):
                                profile_url = f"https://cpaquebec.ca{href}"
                            elif href.startswith("http"):
                                profile_url = href
                            break

                # Если не нашли специфичную ссылку, берем первую ссылку
                if not profile_url:
                    link_elem = card.locator("a").first
                    if await link_elem.count() > 0:
                        href = await link_elem.get_attribute("href")
                        if href and not href.startswith('#') and not href.startswith('javascript'):
                            if href.startswith("/"):
                                profile_url = f"https://cpaquebec.ca{href}"
                            elif href.startswith("http"):
                                profile_url = href
            except:
                pass

            # Извлекаем email и телефон из текста карточки
            email = ""
            phone = ""
            if card_text:
                # Ищем email
                email_match = re.search(r'[\w.+-]+@[\w-]+\.[\w.-]+', card_text)
                if email_match:
                    email = email_match.group()

                # Ищем телефон
                phone_match = re.search(r'(\+\d{1,2}\s?)?(\(\d{3}\)|\d{3})[\s.-]?\d{3}[\s.-]?\d{4}', card_text)
                if phone_match:
                    phone = phone_match.group()

            # Создаем результат только если есть хотя бы имя или контактная информация
            if name != "Unknown" or email or phone or (profile_url and '/profile/' in profile_url):
                result = {
                    "name": name,
                    "profile_url": profile_url,
                    "email": email,
                    "phone": phone,
                    "text": card_text[:200] if card_text else ""
                }

                results.append(result)
                logger.debug(f"Результат {len(results)}: {name}")
            else:
                logger.debug(f"Пропускаем результат #{i+1} - недостаточно данных")

        except Exception as e:
            logger.error(f"Ошибка при обработке результата {i+1}: {e}")

    logger.info(f"Извлечено {len(results)} валидных результатов из {count} элементов")
    return results

async def save_results(results: list, filename: str = None):
    """Сохраняет результаты в JSON файл"""
    if not results:
        logger.warning("Нет результатов для сохранения")
        return

    OUTPUT_DIR.mkdir(exist_ok=True)

    if not filename:
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        filename = f"cpa_results_{timestamp}.json"

    filepath = OUTPUT_DIR / filename

    with open(filepath, "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    logger.info(f"Результаты сохранены: {filepath}")
    logger.info(f"Всего записей: {len(results)}")

async def click_recaptcha_checkbox(page: Page) -> bool:
    """Кликает по галочке 'я не робот' в reCAPTCHA"""
    try:
        # Ищем iframe с reCAPTCHA
        recaptcha_iframe = page.locator("iframe[src*='api2/anchor']").first
        if await recaptcha_iframe.count() == 0:
            logger.debug("reCAPTCHA iframe не найден")
            return False

        # Получаем frame
        frame = await recaptcha_iframe.content_frame()
        if not frame:
            logger.debug("Не удалось получить frame reCAPTCHA")
            return False

        # Ищем чекбокс в frame
        checkbox = frame.locator("#recaptcha-anchor")
        if await checkbox.count() == 0:
            logger.debug("Чекбокс reCAPTCHA не найден")
            return False

        # Кликаем по чекбоксу
        await checkbox.click()
        logger.info("Клик по галочке 'я не робот' выполнен")

        # Ждем немного
        await page.wait_for_timeout(2000)
        return True

    except Exception as e:
        logger.error(f"Ошибка при клике по галочке reCAPTCHA: {e}")
        return False

async def is_recaptcha_solved(page: Page) -> bool:
    """Проверяет, решена ли reCAPTCHA"""
    try:
        # Проверяем наличие токена
        has_token = await page.evaluate("""
            () => {
                const response = document.querySelector('#g-recaptcha-response');
                return response && response.value && response.value.length > 50;
            }
        """)

        if has_token:
            return True

        # Проверяем состояние чекбокса
        recaptcha_iframe = page.locator("iframe[src*='api2/anchor']").first
        if await recaptcha_iframe.count() > 0:
            frame = await recaptcha_iframe.content_frame()
            if frame:
                checkbox = frame.locator("#recaptcha-anchor")
                if await checkbox.count() > 0:
                    is_checked = await checkbox.get_attribute("aria-checked")
                    return is_checked == "true"

        return False

    except Exception as e:
        logger.error(f"Ошибка при проверке состояния reCAPTCHA: {e}")
        return False

async def main():
    """Основная функция"""
    parser = argparse.ArgumentParser(description="CPA Quebec Parser с эмуляцией человеческого поведения")
    parser.add_argument("--visible", action="store_true", help="Запуск в видимом режиме")
    parser.add_argument("--category", type=str, help="Конкретная категория для парсинга",
                       choices=list(CLIENT_CATEGORIES.keys()))
    parser.add_argument("--categories", type=str, nargs='+', help="Несколько категорий для парсинга",
                       choices=list(CLIENT_CATEGORIES.keys()))
    parser.add_argument("--by-category", action="store_true", help="Парсинг по всем категориям")
    parser.add_argument("--debug", action="store_true", help="Отладочный режим")
    parser.add_argument("--slow", action="store_true", help="Очень медленный режим (максимальные задержки)")
    parser.add_argument("--fast", action="store_true", help="Быстрый режим (минимальные задержки)")

    args = parser.parse_args()

    # Настраиваем глобальные множители задержек в зависимости от режима
    global DELAY_MULTIPLIER
    if args.slow:
        DELAY_MULTIPLIER = 2.0
        logger.info("Включен медленный режим (задержки x2)")
    elif args.fast:
        DELAY_MULTIPLIER = 0.5
        logger.info("Включен быстрый режим (задержки x0.5)")
    else:
        DELAY_MULTIPLIER = 1.0

    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)

    # Проверяем наличие ключа Anti-Captcha
    if ANTICAPTCHA_KEY:
        logger.info(f"Anti-Captcha ключ найден: {ANTICAPTCHA_KEY[:5]}...{ANTICAPTCHA_KEY[-5:]}")
    else:
        logger.warning("ANTICAPTCHA_API_KEY не найден в .env файле. Будет использоваться ручное решение капчи.")

    async with async_playwright() as p:
        # Запускаем браузер с дополнительными опциями для обхода детекции
        browser = await p.chromium.launch(
            headless=not args.visible,
            args=[
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-infobars',
                '--ignore-certificate-errors',
                '--ignore-ssl-errors',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-dev-shm-usage',
                '--no-first-run',
                '--no-default-browser-check',
                '--disable-default-apps',
                '--disable-popup-blocking',
                '--disable-translate',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-field-trial-config',
                '--disable-back-forward-cache',
                '--disable-ipc-flooding-protection'
            ]
        )

        # Создаем контекст с реалистичными настройками
        context = await browser.new_context(
            viewport={"width": 1280, "height": 800},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            locale='en-US',
            timezone_id='America/New_York',
            permissions=['geolocation'],
            extra_http_headers={
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-User': '?1',
                'Sec-Fetch-Dest': 'document',
                'Cache-Control': 'max-age=0'
            }
        )

        # Применяем стелс-настройки
        await setup_stealth_browser(context)

        page = await context.new_page()

        try:
            all_results = []

            if args.categories:
                # Парсим указанные категории
                results = await process_multiple_categories(page, args.categories)
                all_results.extend(results)
            elif args.category:
                # Парсим одну категорию (но используем новую логику)
                results = await process_multiple_categories(page, [args.category])
                all_results.extend(results)
            elif args.by_category:
                # Парсим группами по 3 категории
                categories_list = list(CLIENT_CATEGORIES.keys())
                for i in range(0, len(categories_list), 3):
                    category_group = categories_list[i:i+3]
                    logger.info(f"\n=== Начинаем обработку группы категорий: {', '.join(category_group)} ===")
                    results = await process_multiple_categories(page, category_group)
                    all_results.extend(results)

                    # Пауза между группами
                    if i + 3 < len(categories_list):
                        await page.wait_for_timeout(5000)
            else:
                # Парсим с популярными категориями по умолчанию
                default_categories = ["Individuals", "SMEs", "Self-employed workers"]
                results = await process_multiple_categories(page, default_categories)
                all_results.extend(results)

            # Сохраняем результаты
            await save_results(all_results)

        except Exception as e:
            logger.error(f"Критическая ошибка: {e}")
        finally:
            await close_anticaptcha_session()
            await browser.close()

if __name__ == "__main__":
    asyncio.run(main())