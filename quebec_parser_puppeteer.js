#!/usr/bin/env node

/**
 * CPA Quebec Parser с эмуляцией человеческого поведения (Puppeteer версия)
 * Основан на реальном анализе структуры сайта
 * С автоматическим решением капчи через Anti-Captcha API
 */

const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const fs = require('fs').promises;
const path = require('path');
const axios = require('axios');
require('dotenv').config();

// Подключаем stealth plugin
puppeteer.use(StealthPlugin());

// Константы
const BASE_URL = 'https://cpaquebec.ca/en/find-a-cpa/cpa-directory/';
const OUTPUT_DIR = './output';
const ANTICAPTCHA_KEY = process.env.ANTICAPTCHA_API_KEY;
const TWOCAPTCHA_KEY = process.env.TWOCAPTCHA_API_KEY;

// Глобальный множитель задержек
let DELAY_MULTIPLIER = 1.0;

// Категории клиентов
const CLIENT_CATEGORIES = {
    "Individuals": "ListeClienteleDesserviesLeftColumn_0__Selected",
    "Large companies": "ListeClienteleDesserviesLeftColumn_1__Selected",
    "NFPOs": "ListeClienteleDesserviesLeftColumn_2__Selected",
    "Professional firms": "ListeClienteleDesserviesLeftColumn_3__Selected",
    "Public corporations": "ListeClienteleDesserviesLeftColumn_4__Selected",
    "Retailers": "ListeClienteleDesserviesRightColumn_0__Selected",
    "Self-employed workers": "ListeClienteleDesserviesRightColumn_1__Selected",
    "SMEs": "ListeClienteleDesserviesRightColumn_2__Selected",
    "Start-ups": "ListeClienteleDesserviesRightColumn_3__Selected",
    "Syndicates of co-owners": "ListeClienteleDesserviesRightColumn_4__Selected"
};

// === ФУНКЦИИ ЭМУЛЯЦИИ ЧЕЛОВЕЧЕСКОГО ПОВЕДЕНИЯ ===

async function humanDelay(minMs = 500, maxMs = 2000, reason = '') {
    const delay = (Math.random() * (maxMs - minMs) + minMs) * DELAY_MULTIPLIER;
    if (reason) {
        console.log(`🕐 Человеческая пауза ${(delay/1000).toFixed(2)}с (${reason})`);
    } else {
        console.log(`🕐 Человеческая пауза ${(delay/1000).toFixed(2)}с`);
    }
    await new Promise(resolve => setTimeout(resolve, delay));
}

async function humanMouseMovement(page, targetSelector = null) {
    try {
        const viewport = page.viewport();
        const width = viewport.width || 1280;
        const height = viewport.height || 800;

        // Генерируем случайные начальные координаты
        const startX = Math.random() * (width - 200) + 100;
        const startY = Math.random() * (height - 200) + 100;

        let endX, endY;

        if (targetSelector) {
            try {
                const element = await page.$(targetSelector);
                if (element) {
                    const box = await element.boundingBox();
                    if (box) {
                        endX = box.x + box.width / 2;
                        endY = box.y + box.height / 2;
                    } else {
                        endX = Math.random() * (width - 200) + 100;
                        endY = Math.random() * (height - 200) + 100;
                    }
                } else {
                    endX = Math.random() * (width - 200) + 100;
                    endY = Math.random() * (height - 200) + 100;
                }
            } catch (e) {
                endX = Math.random() * (width - 200) + 100;
                endY = Math.random() * (height - 200) + 100;
            }
        } else {
            endX = Math.random() * (width - 200) + 100;
            endY = Math.random() * (height - 200) + 100;
        }

        // Создаем плавную траекторию
        const steps = Math.floor(Math.random() * 10) + 5;
        for (let i = 0; i < steps; i++) {
            const progress = i / (steps - 1);
            const curveOffset = Math.sin(progress * Math.PI) * (Math.random() * 40 - 20);

            const currentX = startX + (endX - startX) * progress + curveOffset;
            const currentY = startY + (endY - startY) * progress + curveOffset;

            await page.mouse.move(currentX, currentY);
            await new Promise(resolve => setTimeout(resolve, Math.random() * 50 + 10));
        }

        console.log(`🖱️ Движение мыши: (${startX.toFixed(0)}, ${startY.toFixed(0)}) -> (${endX.toFixed(0)}, ${endY.toFixed(0)})`);

    } catch (error) {
        console.log(`❌ Ошибка при движении мыши: ${error.message}`);
    }
}

async function humanScroll(page, direction = 'down', amount = null) {
    try {
        if (!amount) {
            amount = Math.random() * 600 + 200;
        }

        const deltaY = direction === 'down' ? amount : -amount;
        const steps = Math.floor(Math.random() * 5) + 3;
        const stepSize = deltaY / steps;

        for (let i = 0; i < steps; i++) {
            await page.mouse.wheel({ deltaY: stepSize });
            await new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50));
        }

        console.log(`📜 Прокрутка ${direction} на ${amount.toFixed(0)}px`);

    } catch (error) {
        console.log(`❌ Ошибка при прокрутке: ${error.message}`);
    }
}

async function humanClick(page, selector, delayBefore = true, delayAfter = true) {
    try {
        if (delayBefore) {
            await humanDelay(300, 1500, 'перед кликом');
        }

        // Движение мыши к элементу
        await humanMouseMovement(page, selector);
        await humanDelay(100, 500, 'наведение на элемент');

        // Клик
        const element = await page.$(selector);
        if (element) {
            await element.click();
            console.log(`👆 Человеческий клик по: ${selector}`);
        } else {
            console.log(`⚠️ Элемент не найден для клика: ${selector}`);
            return false;
        }

        if (delayAfter) {
            await humanDelay(200, 1000, 'после клика');
        }

        return true;

    } catch (error) {
        console.error(`❌ Ошибка при человеческом клике по ${selector}: ${error.message}`);
        return false;
    }
}

async function humanType(page, selector, text, clearFirst = true) {
    try {
        const element = await page.$(selector);
        if (!element) {
            console.log(`⚠️ Элемент не найден для ввода: ${selector}`);
            return false;
        }

        // Фокусируемся на элементе
        await element.focus();
        await humanDelay(200, 500, 'фокус на поле');

        if (clearFirst) {
            await element.click({ clickCount: 3 }); // Выделяем весь текст
            await humanDelay(100, 300, 'очистка поля');
        }

        // Вводим текст посимвольно
        for (const char of text) {
            await element.type(char);

            // Варьируем скорость ввода
            let delay;
            if (char === ' ') {
                delay = Math.random() * 200 + 100; // Пробелы быстрее
            } else if (char === char.toUpperCase() && char !== char.toLowerCase()) {
                delay = Math.random() * 250 + 150; // Заглавные медленнее
            } else {
                delay = Math.random() * 200 + 50; // Обычные символы
            }

            await new Promise(resolve => setTimeout(resolve, delay));
        }

        console.log(`⌨️ Человеческий ввод в ${selector}: ${text}`);
        await humanDelay(200, 600, 'после ввода');

        return true;

    } catch (error) {
        console.error(`❌ Ошибка при человеческом вводе в ${selector}: ${error.message}`);
        return false;
    }
}

async function simulateReadingPage(page, minTime = 3000, maxTime = 8000) {
    try {
        const readingTime = Math.random() * (maxTime - minTime) + minTime;
        console.log(`📖 Симуляция чтения страницы ${(readingTime/1000).toFixed(1)}с`);

        const startTime = Date.now();

        while (Date.now() - startTime < readingTime) {
            const action = ['scroll', 'mouse_move', 'pause'][Math.floor(Math.random() * 3)];

            if (action === 'scroll') {
                const direction = Math.random() > 0.25 ? 'down' : 'up'; // Больше вероятность вниз
                await humanScroll(page, direction, Math.random() * 300 + 100);
            } else if (action === 'mouse_move') {
                await humanMouseMovement(page);
            } else {
                await humanDelay(500, 2000, 'пауза при чтении');
            }
        }

    } catch (error) {
        console.log(`❌ Ошибка при симуляции чтения: ${error.message}`);
    }
}

// === ФУНКЦИИ 2CAPTCHA ===

async function create2CaptchaTask(siteKey, pageUrl) {
    if (!TWOCAPTCHA_KEY) {
        console.error('❌ TWOCAPTCHA_API_KEY не установлен');
        return { success: false, taskId: null };
    }

    console.log(`🔐 Создание задачи 2captcha, sitekey=${siteKey.substring(0, 8)}...`);

    const params = new URLSearchParams({
        key: TWOCAPTCHA_KEY,
        method: 'userrecaptcha',
        googlekey: siteKey,
        pageurl: pageUrl,
        json: 1
    });

    try {
        const response = await axios.post('http://2captcha.com/in.php', params, {
            timeout: 30000,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        });

        const data = response.data;
        console.log('📝 2captcha ответ:', data);

        if (data.status !== 1 || !data.request) {
            console.error(`❌ Ошибка 2captcha: ${data.error_text || 'Unknown error'}`);
            return { success: false, taskId: null };
        }

        console.log(`✅ Задача 2captcha создана, ID: ${data.request}`);
        return { success: true, taskId: data.request };

    } catch (error) {
        console.error(`❌ Ошибка при создании задачи 2captcha: ${error.message}`);
        return { success: false, taskId: null };
    }
}

// === ФУНКЦИИ ANTI-CAPTCHA (FALLBACK) ===

async function createCaptchaTask(siteKey, pageUrl) {
    if (!ANTICAPTCHA_KEY) {
        console.error('❌ ANTICAPTCHA_API_KEY не установлен');
        return { success: false, taskId: null };
    }

    console.log(`🔐 Создание задачи Anti-Captcha, sitekey=${siteKey.substring(0, 8)}...`);

    const taskPayload = {
        clientKey: ANTICAPTCHA_KEY,
        task: {
            type: 'NoCaptchaTaskProxyless',
            websiteURL: pageUrl,
            websiteKey: siteKey,
        },
        softId: 8041
    };

    try {
        const response = await axios.post('https://api.anti-captcha.com/createTask', taskPayload, {
            timeout: 30000
        });

        const data = response.data;
        console.log('📝 Anti-Captcha ответ:', data);

        if (data.errorId > 0 || !data.taskId) {
            console.error(`❌ Ошибка Anti-Captcha: ${data.errorCode} - ${data.errorDescription}`);
            return { success: false, taskId: null };
        }

        console.log(`✅ Задача Anti-Captcha создана, ID: ${data.taskId}`);
        return { success: true, taskId: data.taskId };

    } catch (error) {
        console.error(`❌ Ошибка при создании задачи Anti-Captcha: ${error.message}`);
        return { success: false, taskId: null };
    }
}

async function get2CaptchaResult(taskId, maxAttempts = 60) {
    if (!TWOCAPTCHA_KEY) {
        return { success: false, token: null };
    }

    console.log(`⏳ Ожидание результата 2captcha для задачи ${taskId}...`);

    await new Promise(resolve => setTimeout(resolve, 10000)); // Начальная задержка для 2captcha

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
        try {
            const params = new URLSearchParams({
                key: TWOCAPTCHA_KEY,
                action: 'get',
                id: taskId,
                json: 1
            });

            const response = await axios.get(`http://2captcha.com/res.php?${params}`, {
                timeout: 30000
            });

            const data = response.data;

            if (data.status === 1 && data.request) {
                const token = data.request;
                if (token && token.length > 50) {
                    console.log(`✅ Капча решена 2captcha! Длина токена: ${token.length}`);
                    return { success: true, token: token };
                } else {
                    console.error('❌ 2captcha вернул пустой токен');
                    return { success: false, token: null };
                }
            } else if (data.request === 'CAPCHA_NOT_READY') {
                console.log(`⏳ Задача ${taskId} обрабатывается 2captcha... (попытка ${attempt + 1})`);
            } else if (data.error_text) {
                console.error(`❌ Ошибка 2captcha: ${data.error_text}`);
                return { success: false, token: null };
            } else {
                console.log(`⚠️ Неизвестный ответ 2captcha:`, data);
            }

            await new Promise(resolve => setTimeout(resolve, 5000)); // 2captcha рекомендует 5 сек

        } catch (error) {
            console.error(`❌ Ошибка при получении результата 2captcha: ${error.message}`);
            await new Promise(resolve => setTimeout(resolve, 5000));
        }
    }

    console.error(`❌ Превышено время ожидания для задачи 2captcha ${taskId}`);
    return { success: false, token: null };
}

async function getCaptchaResult(taskId, maxAttempts = 60) {
    if (!ANTICAPTCHA_KEY) {
        return { success: false, token: null };
    }

    console.log(`⏳ Ожидание результата Anti-Captcha для задачи ${taskId}...`);

    await new Promise(resolve => setTimeout(resolve, 5000)); // Начальная задержка

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
        try {
            const response = await axios.post('https://api.anti-captcha.com/getTaskResult', {
                clientKey: ANTICAPTCHA_KEY,
                taskId: taskId
            }, { timeout: 30000 });

            const data = response.data;

            if (data.errorId > 0) {
                console.error(`❌ Ошибка Anti-Captcha: ${data.errorCode} - ${data.errorDescription}`);
                return { success: false, token: null };
            }

            if (data.status === 'ready') {
                const token = data.solution?.gRecaptchaResponse;
                if (token && token.length > 50) {
                    console.log(`✅ Капча решена! Длина токена: ${token.length}`);
                    return { success: true, token: token };
                } else {
                    console.error('❌ Anti-Captcha вернул пустой токен');
                    return { success: false, token: null };
                }
            } else if (data.status === 'processing') {
                console.log(`⏳ Задача ${taskId} обрабатывается... (попытка ${attempt + 1})`);
            } else {
                console.log(`⚠️ Неизвестный статус: ${data.status}`);
            }

            await new Promise(resolve => setTimeout(resolve, 3000));

        } catch (error) {
            console.error(`❌ Ошибка при получении результата: ${error.message}`);
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
    }

    console.error(`❌ Превышено время ожидания для задачи ${taskId}`);
    return { success: false, token: null };
}

// === ФУНКЦИИ РАБОТЫ С КАПЧЕЙ ===

async function extractRecaptchaSitekey(page) {
    try {
        const sitekey = await page.evaluate(() => {
            // Ищем в div.g-recaptcha
            const recaptchaDiv = document.querySelector('.g-recaptcha');
            if (recaptchaDiv && recaptchaDiv.getAttribute('data-sitekey')) {
                return recaptchaDiv.getAttribute('data-sitekey');
            }

            // Ищем в iframe
            const iframes = document.querySelectorAll('iframe[src*="recaptcha"]');
            for (const iframe of iframes) {
                const src = iframe.src;
                const match = src.match(/k=([^&]+)/);
                if (match) {
                    return match[1];
                }
            }

            // Ищем в любом элементе с data-sitekey
            const element = document.querySelector('[data-sitekey]');
            if (element) {
                return element.getAttribute('data-sitekey');
            }

            return null;
        });

        if (sitekey) {
            console.log(`🔑 Найден sitekey: ${sitekey.substring(0, 10)}...`);
            return { success: true, sitekey };
        } else {
            console.error('❌ Sitekey не найден');
            return { success: false, sitekey: null };
        }
    } catch (error) {
        console.error(`❌ Ошибка при извлечении sitekey: ${error.message}`);
        return { success: false, sitekey: null };
    }
}

async function insertCaptchaToken(page, token) {
    try {
        await page.evaluate((token) => {
            // Находим или создаем поле g-recaptcha-response
            let response = document.querySelector('#g-recaptcha-response');
            if (!response) {
                response = document.createElement('textarea');
                response.id = 'g-recaptcha-response';
                response.name = 'g-recaptcha-response';
                response.className = 'g-recaptcha-response';
                response.style.display = 'none';
                document.body.appendChild(response);
            }

            // Вставляем токен
            response.value = token;
            response.dispatchEvent(new Event('change', { bubbles: true }));

            // Вызываем callback если есть
            if (window.grecaptcha && window.___grecaptcha_cfg) {
                const widgets = window.___grecaptcha_cfg.clients;
                for (const clientId in widgets) {
                    const client = widgets[clientId];
                    if (client.callback) {
                        client.callback(token);
                    }
                }
            }

            console.log('Токен капчи вставлен:', token.substring(0, 20) + '...');
        }, token);

        console.log('✅ Токен капчи успешно вставлен');
        return true;
    } catch (error) {
        console.error(`❌ Ошибка при вставке токена: ${error.message}`);
        return false;
    }
}

async function clickRecaptchaCheckboxHuman(page) {
    try {
        // Ищем iframe с reCAPTCHA
        const recaptchaFrame = await page.$('iframe[src*="api2/anchor"]');
        if (!recaptchaFrame) {
            console.log('⚠️ reCAPTCHA iframe не найден');
            return false;
        }

        // Симулируем движение мыши к области капчи
        await humanMouseMovement(page, 'iframe[src*="api2/anchor"]');
        await humanDelay(500, 1500, 'изучение капчи');

        // Получаем frame
        const frame = await recaptchaFrame.contentFrame();
        if (!frame) {
            console.log('⚠️ Не удалось получить frame reCAPTCHA');
            return false;
        }

        // Ищем чекбокс в frame
        const checkbox = await frame.$('#recaptcha-anchor');
        if (!checkbox) {
            console.log('⚠️ Чекбокс reCAPTCHA не найден');
            return false;
        }

        // Человеческая задержка перед кликом
        await humanDelay(800, 2000, 'размышление перед кликом по капче');

        // Кликаем по чекбоксу
        await checkbox.click();
        console.log('✅ Человеческий клик по галочке "я не робот" выполнен');

        // Человеческая задержка после клика
        await humanDelay(1000, 2500, 'ожидание после клика по капче');
        return true;

    } catch (error) {
        console.error(`❌ Ошибка при человеческом клике по галочке reCAPTCHA: ${error.message}`);
        return false;
    }
}

async function isRecaptchaSolved(page) {
    try {
        const hasToken = await page.evaluate(() => {
            const response = document.querySelector('#g-recaptcha-response');
            return response && response.value && response.value.length > 50;
        });

        if (hasToken) {
            return true;
        }

        // Проверяем состояние чекбокса
        const recaptchaFrame = await page.$('iframe[src*="api2/anchor"]');
        if (recaptchaFrame) {
            const frame = await recaptchaFrame.contentFrame();
            if (frame) {
                const checkbox = await frame.$('#recaptcha-anchor');
                if (checkbox) {
                    const isChecked = await frame.evaluate(el => el.getAttribute('aria-checked'), checkbox);
                    return isChecked === 'true';
                }
            }
        }

        return false;

    } catch (error) {
        console.error(`❌ Ошибка при проверке состояния reCAPTCHA: ${error.message}`);
        return false;
    }
}

async function simulateWaitingBehavior(page) {
    try {
        // Случайные действия во время ожидания
        const actions = ['mouse_move', 'scroll', 'pause', 'mouse_move'];

        for (let i = 0; i < Math.floor(Math.random() * 5) + 3; i++) {
            const action = actions[Math.floor(Math.random() * actions.length)];

            if (action === 'mouse_move') {
                await humanMouseMovement(page);
            } else if (action === 'scroll') {
                const direction = Math.random() > 0.5 ? 'down' : 'up';
                await humanScroll(page, direction, Math.random() * 150 + 50);
            } else {
                await humanDelay(1000, 3000, 'пауза во время ожидания');
            }

            await humanDelay(500, 1500, 'между действиями ожидания');
        }

    } catch (error) {
        console.log(`❌ Ошибка при симуляции ожидания: ${error.message}`);
    }
}

async function waitForCaptchaWindowToClose(page) {
    try {
        console.log('🔍 Проверяем наличие окна визуальной капчи...');

        // Ищем iframe с визуальной капчей (challenge)
        const challengeSelectors = [
            'iframe[src*="api2/bframe"]',
            'iframe[src*="recaptcha/api2/bframe"]',
            'iframe[title*="recaptcha challenge"]',
            'iframe[title="recaptcha challenge expires in two minutes"]'
        ];

        // Проверяем наличие окна капчи
        let challengeFound = false;
        for (const selector of challengeSelectors) {
            try {
                const challengeFrame = await page.$(selector);
                if (challengeFrame) {
                    const isVisible = await page.evaluate(el => {
                        const rect = el.getBoundingClientRect();
                        const style = window.getComputedStyle(el);
                        return rect.width > 0 && rect.height > 0 &&
                               style.visibility !== 'hidden' &&
                               style.display !== 'none' &&
                               style.opacity !== '0';
                    }, challengeFrame);

                    if (isVisible) {
                        challengeFound = true;
                        console.log(`📋 Найдено окно визуальной капчи: ${selector}`);
                        break;
                    }
                }
            } catch (e) {
                // Продолжаем поиск
            }
        }

        if (!challengeFound) {
            console.log('✅ Окно визуальной капчи не найдено');
            return true;
        }

        // Ждем исчезновения окна капчи
        console.log('⏳ Ожидаем закрытия окна визуальной капчи...');

        for (let attempt = 0; attempt < 30; attempt++) {
            let stillVisible = false;

            for (const selector of challengeSelectors) {
                try {
                    const challengeFrame = await page.$(selector);
                    if (challengeFrame) {
                        const isVisible = await page.evaluate(el => {
                            const rect = el.getBoundingClientRect();
                            const style = window.getComputedStyle(el);
                            return rect.width > 0 && rect.height > 0 &&
                                   style.visibility !== 'hidden' &&
                                   style.display !== 'none' &&
                                   style.opacity !== '0';
                        }, challengeFrame);

                        if (isVisible) {
                            stillVisible = true;
                            break;
                        }
                    }
                } catch (e) {
                    // Элемент исчез - это хорошо
                }
            }

            if (!stillVisible) {
                console.log('✅ Окно визуальной капчи закрылось!');
                await humanDelay(1000, 2000, 'стабилизация после закрытия капчи');
                return true;
            }

            console.log(`⏳ Окно капчи все еще видимо, ждем... (попытка ${attempt + 1}/30)`);
            await humanDelay(1000, 2000, 'ожидание закрытия окна капчи');
        }

        console.log('⚠️ Окно капчи не закрылось за отведенное время');
        return false;

    } catch (error) {
        console.log(`❌ Ошибка при ожидании закрытия окна капчи: ${error.message}`);
        return false;
    }
}

async function clickSearchButtonHuman(page) {
    try {
        console.log('🔍 Подготовка к нажатию кнопки поиска...');

        // Проверяем, что окно капчи точно исчезло
        console.log('🔍 Финальная проверка отсутствия окна капчи...');
        const captchaWindowClosed = await waitForCaptchaWindowToClose(page);
        if (!captchaWindowClosed) {
            console.log('⚠️ Окно капчи все еще видимо, но продолжаем...');
        }

        // Симулируем финальную проверку формы
        await simulateReadingPage(page, 1000, 2500);

        // Ищем кнопку поиска
        const searchSelectors = [
            'form#FindACPABottinForm button[type="submit"]',
            'button:contains("SEARCH")',
            'button[type="submit"]',
            'input[type="submit"]'
        ];

        let searchButton = null;
        let usedSelector = null;

        for (const selector of searchSelectors) {
            try {
                searchButton = await page.$(selector);
                if (searchButton) {
                    usedSelector = selector;
                    console.log(`🎯 Найдена кнопка поиска: ${selector}`);
                    break;
                }
            } catch (e) {
                // Продолжаем поиск
            }
        }

        if (!searchButton) {
            console.log('⚠️ Кнопка поиска не найдена, пробуем JavaScript');
            // Fallback через JavaScript
            const result = await page.evaluate(() => {
                // Ищем форму поиска CPA
                const form = document.querySelector('#FindACPABottinForm');
                if (form) {
                    const button = form.querySelector('button[type="submit"]');
                    if (button) {
                        button.click();
                        console.log('Clicked search button via JS (form)');
                        return true;
                    }
                }

                // Ищем любую кнопку поиска
                const buttons = document.querySelectorAll('button[type="submit"]');
                for (const btn of buttons) {
                    if (btn.textContent && btn.textContent.toLowerCase().includes('search')) {
                        btn.click();
                        console.log('Clicked search button via JS (any)');
                        return true;
                    }
                }

                // Если не найдена, отправляем первую форму
                const forms = document.querySelectorAll('form');
                if (forms.length > 0) {
                    forms[0].submit();
                    console.log('Submitted first form via JS');
                    return true;
                }

                return false;
            });

            if (result) {
                console.log('✅ Кнопка поиска нажата (через JavaScript)');
                await humanDelay(1000, 2000, 'после нажатия кнопки поиска');
                return true;
            } else {
                console.error('❌ Кнопка поиска не найдена');
                return false;
            }
        }

        // Человеческое взаимодействие с кнопкой
        console.log(`👆 Нажимаем кнопку поиска с человеческим поведением: ${usedSelector}`);

        // Прокручиваем к кнопке
        await searchButton.scrollIntoView();
        await humanDelay(300, 800, 'прокрутка к кнопке поиска');

        // Движение мыши к кнопке
        await humanMouseMovement(page, usedSelector);
        await humanDelay(500, 1500, 'наведение на кнопку поиска');

        // Финальная пауза перед кликом (имитация размышления)
        await humanDelay(800, 2000, 'размышление перед поиском');

        // Клик по кнопке с дополнительными опциями
        try {
            await searchButton.click();
            console.log('✅ Человеческий клик по кнопке поиска выполнен');
        } catch (error) {
            console.log(`⚠️ Обычный клик не сработал: ${error.message}, пробуем force click`);
            try {
                await searchButton.click({ force: true });
                console.log('✅ Force клик по кнопке поиска выполнен');
            } catch (error2) {
                console.log(`⚠️ Force клик не сработал: ${error2.message}, пробуем JavaScript`);
                await page.evaluate(el => el.click(), searchButton);
                console.log('✅ JavaScript клик по кнопке поиска выполнен');
            }
        }

        // Пауза после клика
        await humanDelay(1000, 2500, 'ожидание после нажатия поиска');

        return true;

    } catch (error) {
        console.error(`❌ Ошибка при человеческом нажатии кнопки поиска: ${error.message}`);
        return false;
    }
}

async function solveCaptchaAuto(page) {
    // Проверяем доступность сервисов капчи
    const has2Captcha = !!TWOCAPTCHA_KEY;
    const hasAntiCaptcha = !!ANTICAPTCHA_KEY;

    if (!has2Captcha && !hasAntiCaptcha) {
        console.log('⚠️ Ключи капча-сервисов не установлены, используем ручное решение');
        return await waitForCaptchaManual(page);
    }

    // Симулируем изучение страницы перед взаимодействием с капчей
    console.log('📖 Изучаем страницу перед решением капчи...');
    await simulateReadingPage(page, 2000, 5000);

    // Сначала кликаем по галочке "я не робот" с человеческим поведением
    console.log('👆 Кликаем по галочке "я не робот" с эмуляцией человека...');
    await clickRecaptchaCheckboxHuman(page);

    // Ждем с человеческими задержками и проверяем, может капча решилась автоматически
    await humanDelay(2000, 4000, 'ожидание после клика по капче');
    if (await isRecaptchaSolved(page)) {
        console.log('✅ Капча решена автоматически после клика!');
        return true;
    }

    // Извлекаем sitekey
    const { success: sitekeySuccess, sitekey } = await extractRecaptchaSitekey(page);
    if (!sitekeySuccess || !sitekey) {
        console.error('❌ Не удалось извлечь sitekey');
        return false;
    }

    // Пробуем сначала 2captcha, потом Anti-Captcha
    let taskSuccess = false;
    let taskId = null;
    let service = '';

    if (has2Captcha) {
        console.log('🔐 Пробуем 2captcha...');
        const result = await create2CaptchaTask(sitekey, BASE_URL);
        if (result.success) {
            taskSuccess = true;
            taskId = result.taskId;
            service = '2captcha';
        } else {
            console.log('⚠️ 2captcha не сработал, пробуем Anti-Captcha...');
        }
    }

    if (!taskSuccess && hasAntiCaptcha) {
        console.log('🔐 Используем Anti-Captcha...');
        const result = await createCaptchaTask(sitekey, BASE_URL);
        if (result.success) {
            taskSuccess = true;
            taskId = result.taskId;
            service = 'anticaptcha';
        }
    }

    if (!taskSuccess || !taskId) {
        console.error('❌ Не удалось создать задачу ни в одном сервисе');
        return await waitForCaptchaManual(page);
    }

    // Симулируем ожидание пользователя во время решения капчи
    console.log(`🎭 Симулируем человеческое поведение во время решения капчи (${service})...`);
    await simulateWaitingBehavior(page);

    // Получаем результат в зависимости от сервиса
    let resultSuccess = false;
    let token = null;

    if (service === '2captcha') {
        const result = await get2CaptchaResult(taskId);
        resultSuccess = result.success;
        token = result.token;
    } else {
        const result = await getCaptchaResult(taskId);
        resultSuccess = result.success;
        token = result.token;
    }

    if (!resultSuccess || !token) {
        console.error(`❌ Не удалось получить токен от ${service}`);
        return await waitForCaptchaManual(page);
    }

    // Вставляем токен с задержкой
    await humanDelay(1000, 2000, 'перед вставкой токена');
    if (!await insertCaptchaToken(page, token)) {
        console.error('❌ Не удалось вставить токен');
        return false;
    }

    // Проверяем, что капча решена с человеческой задержкой
    await humanDelay(1500, 3000, 'проверка решения капчи');
    if (await isRecaptchaSolved(page)) {
        console.log(`✅ Капча автоматически решена через ${service}!`);

        // Ждем исчезновения окна с визуальной капчей
        console.log('⏳ Ожидаем исчезновения окна капчи...');
        await waitForCaptchaWindowToClose(page);

        return true;
    } else {
        console.log('⚠️ Токен вставлен, но капча не считается решенной');
        // Пробуем еще раз кликнуть по галочке с человеческим поведением
        await clickRecaptchaCheckboxHuman(page);
        await humanDelay(2000, 4000, 'повторная проверка капчи');

        const solved = await isRecaptchaSolved(page);
        if (solved) {
            console.log('⏳ Ожидаем исчезновения окна капчи...');
            await waitForCaptchaWindowToClose(page);
        }
        return solved;
    }
}

async function waitForCaptchaManual(page) {
    console.log('⚠️ ВНИМАНИЕ: Обнаружена reCAPTCHA!');
    console.log('👤 Пожалуйста, решите капчу вручную в браузере и нажмите Enter для продолжения...');

    // Проверяем, есть ли уже решенная капча
    let token = await page.evaluate(() => {
        const response = document.querySelector('#g-recaptcha-response');
        return response && response.value && response.value.length > 50;
    });

    if (!token) {
        // Ждем ручного ввода
        const readline = require('readline');
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        await new Promise(resolve => {
            rl.question('Нажмите Enter после решения капчи: ', () => {
                rl.close();
                resolve();
            });
        });

        // Проверяем еще раз
        token = await page.evaluate(() => {
            const response = document.querySelector('#g-recaptcha-response');
            return response && response.value && response.value.length > 50;
        });
    }

    if (token) {
        console.log('✅ Капча решена!');
        return true;
    } else {
        console.log('❌ Капча не решена');
        return false;
    }
}

async function handleCaptcha(page) {
    // Проверяем наличие капчи
    const captchaExists = await page.evaluate(() => {
        return document.querySelector('.g-recaptcha') !== null ||
               document.querySelector('iframe[src*="recaptcha"]') !== null;
    });

    if (!captchaExists) {
        console.log('ℹ️ Капча не найдена');
        return true;
    }

    // Пробуем автоматическое решение
    return await solveCaptchaAuto(page);
}

// === ФУНКЦИИ РАБОТЫ С ФОРМАМИ ===

async function closeCookiesBannerHuman(page) {
    try {
        const cookieBanner = await page.$('text=Tout refuser');
        if (cookieBanner) {
            console.log('🍪 Обнаружен cookie banner, закрываем с человеческим поведением...');

            // Симулируем чтение баннера
            await humanDelay(1000, 2500, 'чтение cookie banner');

            // Движение мыши к кнопке
            await humanMouseMovement(page, 'text=Tout refuser');
            await humanDelay(300, 800, 'наведение на кнопку отказа');

            // Клик
            await cookieBanner.click();
            console.log('✅ Cookie banner закрыт');

            await humanDelay(1000, 2000, 'после закрытия banner');
            return true;
        }
    } catch (error) {
        // Игнорируем ошибки
    }
    console.log('ℹ️ Cookie banner не найден или уже закрыт');
    return false;
}

async function selectMultipleCategories(page, categories) {
    console.log(`📋 Выбираем категории с человеческим поведением: ${categories.join(', ')}`);

    // Симулируем изучение формы перед выбором
    await simulateReadingPage(page, 1500, 3000);

    // Сначала снимаем все чекбоксы с человеческими задержками
    console.log('🔄 Сбрасываем все чекбоксы категорий...');
    await page.evaluate(() => {
        // Снимаем все чекбоксы категорий
        const checkboxes = document.querySelectorAll('input[type="checkbox"][name*="ListeClienteleDesservies"]');
        checkboxes.forEach(checkbox => {
            if (checkbox.checked) {
                checkbox.checked = false;
                checkbox.dispatchEvent(new Event('change', { bubbles: true }));
            }
        });
    });

    await humanDelay(500, 1000, 'после сброса чекбоксов');

    let successCount = 0;

    // Устанавливаем нужные чекбоксы с человеческим поведением
    for (let i = 0; i < categories.length; i++) {
        const category = categories[i];

        if (!CLIENT_CATEGORIES[category]) {
            console.error(`❌ Неизвестная категория: ${category}`);
            continue;
        }

        const checkboxId = CLIENT_CATEGORIES[category];
        console.log(`☑️ Выбираем категорию: ${category} (ID: ${checkboxId})`);

        try {
            const checkbox = await page.$(`#${checkboxId}`);
            if (!checkbox) {
                console.error(`❌ Чекбокс не найден: ${checkboxId}`);
                continue;
            }

            // Прокручиваем к элементу с человеческим поведением
            await checkbox.scrollIntoView();
            await humanDelay(300, 800, `прокрутка к категории ${category}`);

            // Движение мыши к чекбоксу
            await humanMouseMovement(page, `#${checkboxId}`);
            await humanDelay(400, 1200, `изучение категории ${category}`);

            // Человеческий клик по чекбоксу
            await checkbox.click();

            // Проверяем, что чекбокс отмечен
            const isChecked = await page.evaluate(el => el.checked, checkbox);
            if (isChecked) {
                console.log(`✅ Категория '${category}' успешно выбрана`);
                successCount++;
            } else {
                console.error(`❌ Не удалось выбрать категорию '${category}'`);
            }

            // Пауза между выбором категорий (кроме последней)
            if (i < categories.length - 1) {
                await humanDelay(800, 2000, `пауза после выбора ${category}`);
            }

        } catch (error) {
            console.error(`❌ Ошибка при выборе категории '${category}': ${error.message}`);
        }
    }

    // Финальная пауза для "обдумывания" выбора
    await humanDelay(1000, 2500, 'обдумывание выбранных категорий');

    console.log(`📊 Успешно выбрано ${successCount} из ${categories.length} категорий`);
    return successCount > 0;
}



async function extractResults(page) {
    console.log('📊 Извлекаем результаты поиска...');
    const results = [];

    // Ждем загрузки результатов
    try {
        await page.waitForSelector('.search-results, .results, .listing', { timeout: 30000 });
    } catch (error) {
        console.log('⚠️ Селектор результатов не найден, продолжаем');
    }

    // Пробуем различные селекторы для результатов профилей CPA
    const resultSelectors = [
        '.search-result',
        '.result-item',
        '.listing-item',
        '.cpa-listing',
        'article',
        '.card',
        'div.row:has(.cpa-name)',
        'div.row:has(a[href*="/profile/"])',
        'div.row:has(a[href*="/directory/"])',
        'div.row div.col:has(h3)',
        'div.row div.col:has(h2)',
        'div:has(strong):has(a[href*="mailto"])',
        'div:has(h3):has(a[href*="tel"])',
        'tr:has(td)',
        'li.profile',
        'li.member'
    ];

    let cards = null;
    let count = 0;

    // Анализируем страницу с помощью JavaScript
    const jsAnalysis = await page.evaluate(() => {
        // Ищем элементы, которые могут содержать профили CPA
        const possibleResults = [];

        // Метод 1: Поиск элементов с email
        const emailElements = Array.from(document.querySelectorAll('a[href^="mailto"]'));
        emailElements.forEach(elem => {
            const parent = elem.closest('div, tr, li, article');
            if (parent && !possibleResults.includes(parent)) {
                possibleResults.push(parent);
            }
        });

        // Метод 2: Поиск элементов с телефоном
        const phoneElements = Array.from(document.querySelectorAll('a[href^="tel"]'));
        phoneElements.forEach(elem => {
            const parent = elem.closest('div, tr, li, article');
            if (parent && !possibleResults.includes(parent)) {
                possibleResults.push(parent);
            }
        });

        return {
            emailElementsCount: emailElements.length,
            phoneElementsCount: phoneElements.length,
            possibleResultsCount: possibleResults.length,
            pageText: document.body.innerText.substring(0, 500)
        };
    });

    console.log('🔍 JS анализ:', jsAnalysis);

    for (const selector of resultSelectors) {
        try {
            const elements = await page.$$(selector);
            count = elements.length;

            if (count > 0) {
                console.log(`🎯 Пробуем селектор: ${selector}, найдено элементов: ${count}`);

                // Проверяем первый элемент на наличие контактной информации
                if (count > 0) {
                    const firstElement = elements[0];
                    let firstElementText = '';

                    try {
                        firstElementText = await page.evaluate(el => el.innerText, firstElement);
                    } catch (e) {
                        // Игнорируем ошибки
                    }

                    // Проверяем, содержит ли элемент контактную информацию
                    const hasContactInfo = (
                        firstElementText.includes('@') ||
                        firstElementText.includes('tel:') ||
                        firstElementText.includes('CPA') ||
                        /\(\d{3}\)/.test(firstElementText)
                    );

                    if (hasContactInfo) {
                        console.log(`✅ Найдены результаты с контактной информацией с селектором: ${selector}`);
                        cards = elements;
                        break;
                    } else {
                        console.log(`⚠️ Элементы с селектором ${selector} не содержат контактной информации`);
                    }
                }
            }
        } catch (error) {
            console.log(`❌ Ошибка с селектором ${selector}: ${error.message}`);
            continue;
        }
    }

    if (!cards || count === 0) {
        console.log('⚠️ Результаты поиска с контактной информацией не найдены');
        // Попробуем найти хотя бы элементы с именами
        try {
            cards = await page.$$('div:has(h3), div:has(h2), div:has(strong)');
            count = cards.length;
            if (count > 0) {
                console.log(`📋 Найдено ${count} элементов с заголовками как резервный вариант`);
            }
        } catch (e) {
            // Игнорируем ошибки
        }
    }

    if (!cards || count === 0) {
        console.log('❌ Вообще никаких результатов не найдено');
        return results;
    }

    console.log(`🔄 Обрабатываем ${count} результатов...`);

    const maxResults = Math.min(count, 50); // Ограничиваем до 50 результатов

    for (let i = 0; i < maxResults; i++) {
        try {
            const card = cards[i];

            // Получаем весь текст карточки
            let cardText = '';
            try {
                cardText = await page.evaluate(el => el.innerText, card);
            } catch (e) {
                // Игнорируем ошибки
            }

            // Пропускаем элементы навигации и меню
            if (cardText && (
                cardText.includes('Menu') ||
                cardText.includes('General public') ||
                cardText.includes('Back') ||
                cardText.includes('Find a CPA') ||
                cardText.includes('SEARCH') ||
                cardText.includes('Home Se')
            )) {
                console.log(`⏭️ Пропускаем элемент навигации #${i+1}`);
                continue;
            }

            // Извлекаем данные
            const result = await extractCardData(page, card, cardText);

            // Создаем результат только если есть хотя бы имя или контактная информация
            if (result.name !== 'Unknown' || result.email || result.phone || (result.profileUrl && result.profileUrl.includes('/profile/'))) {
                results.push(result);
                console.log(`📝 Результат ${results.length}: ${result.name}`);
            } else {
                console.log(`⏭️ Пропускаем результат #${i+1} - недостаточно данных`);
            }

        } catch (error) {
            console.error(`❌ Ошибка при обработке результата ${i+1}: ${error.message}`);
        }
    }

    console.log(`✅ Извлечено ${results.length} валидных результатов из ${count} элементов`);
    return results;
}

async function extractCardData(page, card, cardText) {
    // Извлекаем имя
    let name = 'Unknown';
    const nameSelectors = ['h3', 'h2', 'h4', '.name', '.title', 'strong', 'b'];

    for (const nameSelector of nameSelectors) {
        try {
            const nameElement = await card.$(nameSelector);
            if (nameElement) {
                const nameText = await page.evaluate(el => el.innerText, nameElement);
                if (nameText && nameText.trim().length > 2 &&
                    !['menu', 'back', 'search', 'home', 'general public'].some(word =>
                        nameText.toLowerCase().includes(word))) {
                    name = nameText.trim();
                    break;
                }
            }
        } catch (e) {
            continue;
        }
    }

    // Извлекаем ссылку на профиль
    let profileUrl = null;
    const linkSelectors = [
        'a[href*="/profile/"]',
        'a[href*="/directory/"]',
        'a[href*="/cpa/"]',
        'a[href*="/member/"]'
    ];

    for (const linkSelector of linkSelectors) {
        try {
            const linkElement = await card.$(linkSelector);
            if (linkElement) {
                const href = await page.evaluate(el => el.getAttribute('href'), linkElement);
                if (href) {
                    if (href.startsWith('/')) {
                        profileUrl = `https://cpaquebec.ca${href}`;
                    } else if (href.startsWith('http')) {
                        profileUrl = href;
                    }
                    break;
                }
            }
        } catch (e) {
            continue;
        }
    }

    // Если не нашли специфичную ссылку, берем первую ссылку
    if (!profileUrl) {
        try {
            const linkElement = await card.$('a');
            if (linkElement) {
                const href = await page.evaluate(el => el.getAttribute('href'), linkElement);
                if (href && !href.startsWith('#') && !href.startsWith('javascript')) {
                    if (href.startsWith('/')) {
                        profileUrl = `https://cpaquebec.ca${href}`;
                    } else if (href.startsWith('http')) {
                        profileUrl = href;
                    }
                }
            }
        } catch (e) {
            // Игнорируем ошибки
        }
    }

    // Извлекаем email и телефон из текста карточки
    let email = '';
    let phone = '';

    if (cardText) {
        // Ищем email
        const emailMatch = cardText.match(/[\w.+-]+@[\w-]+\.[\w.-]+/);
        if (emailMatch) {
            email = emailMatch[0];
        }

        // Ищем телефон
        const phoneMatch = cardText.match(/(\+\d{1,2}\s?)?(\(\d{3}\)|\d{3})[\s.-]?\d{3}[\s.-]?\d{4}/);
        if (phoneMatch) {
            phone = phoneMatch[0];
        }
    }

    return {
        name,
        profileUrl,
        email,
        phone,
        text: cardText ? cardText.substring(0, 200) : ''
    };
}

// === ОСНОВНЫЕ ФУНКЦИИ ПАРСЕРА ===

async function processMultipleCategories(page, categories = null) {
    if (!categories) {
        // По умолчанию выбираем несколько популярных категорий
        categories = ['Individuals', 'SMEs', 'Self-employed workers'];
    }

    console.log(`🎯 === Обработка категорий с человеческим поведением: ${categories.join(', ')} ===`);

    // Переходим на страницу поиска с человеческой задержкой
    console.log('🌐 Переходим на страницу поиска...');
    await page.goto(BASE_URL, { timeout: 60000, waitUntil: 'domcontentloaded' });

    // Симулируем первоначальное изучение страницы
    await simulateReadingPage(page, 3000, 6000);

    // Закрываем cookie banner с человеческим поведением
    await closeCookiesBannerHuman(page);

    // Выбираем несколько категорий с человеческим поведением
    if (!await selectMultipleCategories(page, categories)) {
        console.error('❌ Не удалось выбрать ни одной категории');
        return [];
    }

    // Обрабатываем капчу с человеческим поведением
    if (!await handleCaptcha(page)) {
        console.error('❌ Не удалось решить капчу');
        return [];
    }

    // Нажимаем кнопку поиска с человеческим поведением
    if (!await clickSearchButtonHuman(page)) {
        console.error('❌ Не удалось нажать кнопку поиска');
        return [];
    }

    // Ждем перехода на страницу результатов с человеческими задержками
    console.log('⏳ Ожидаем загрузку результатов...');
    await humanDelay(3000, 6000, 'ожидание результатов поиска');

    // Симулируем изучение страницы результатов
    await simulateReadingPage(page, 2000, 4000);

    // Извлекаем результаты
    const results = await extractResults(page);

    // Добавляем категории к каждому результату
    for (const result of results) {
        result.categories = categories;
    }

    console.log(`✅ Найдено ${results.length} результатов для категорий: ${categories.join(', ')}`);
    return results;
}

async function saveResults(results, filename = null) {
    if (!results || results.length === 0) {
        console.log('⚠️ Нет результатов для сохранения');
        return;
    }

    // Создаем папку output если её нет
    try {
        await fs.mkdir(OUTPUT_DIR, { recursive: true });
    } catch (e) {
        // Папка уже существует
    }

    if (!filename) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        filename = `cpa_results_${timestamp}.json`;
    }

    const filepath = path.join(OUTPUT_DIR, filename);

    try {
        await fs.writeFile(filepath, JSON.stringify(results, null, 2), 'utf8');
        console.log(`💾 Результаты сохранены: ${filepath}`);
        console.log(`📊 Всего записей: ${results.length}`);
    } catch (error) {
        console.error(`❌ Ошибка при сохранении файла: ${error.message}`);
    }
}

// === ОСНОВНАЯ ФУНКЦИЯ ===

async function main() {
    console.log('🚀 CPA Quebec Parser с эмуляцией человеческого поведения (Puppeteer версия)');

    // Парсинг аргументов командной строки
    const args = process.argv.slice(2);
    const options = {
        visible: args.includes('--visible'),
        debug: args.includes('--debug'),
        slow: args.includes('--slow'),
        fast: args.includes('--fast'),
        byCategory: args.includes('--by-category')
    };

    // Получаем категории из аргументов
    let categories = null;
    const categoryIndex = args.indexOf('--category');
    const categoriesIndex = args.indexOf('--categories');

    if (categoryIndex !== -1 && args[categoryIndex + 1]) {
        categories = [args[categoryIndex + 1]];
    } else if (categoriesIndex !== -1) {
        categories = [];
        for (let i = categoriesIndex + 1; i < args.length; i++) {
            if (args[i].startsWith('--')) break;
            if (CLIENT_CATEGORIES[args[i]]) {
                categories.push(args[i]);
            }
        }
    }

    // Настраиваем глобальные множители задержек
    if (options.slow) {
        DELAY_MULTIPLIER = 2.0;
        console.log('🐌 Включен медленный режим (задержки x2)');
    } else if (options.fast) {
        DELAY_MULTIPLIER = 0.5;
        console.log('⚡ Включен быстрый режим (задержки x0.5)');
    } else {
        DELAY_MULTIPLIER = 1.0;
    }

    // Проверяем наличие ключей капча-сервисов
    if (TWOCAPTCHA_KEY) {
        console.log(`🔑 2captcha ключ найден: ${TWOCAPTCHA_KEY.substring(0, 5)}...${TWOCAPTCHA_KEY.slice(-5)}`);
    }
    if (ANTICAPTCHA_KEY) {
        console.log(`🔑 Anti-Captcha ключ найден: ${ANTICAPTCHA_KEY.substring(0, 5)}...${ANTICAPTCHA_KEY.slice(-5)}`);
    }
    if (!TWOCAPTCHA_KEY && !ANTICAPTCHA_KEY) {
        console.log('⚠️ Ключи капча-сервисов не найдены в .env файле. Будет использоваться ручное решение капчи.');
        console.log('💡 Добавьте TWOCAPTCHA_API_KEY или ANTICAPTCHA_API_KEY в .env файл для автоматического решения.');
    }

    // Запускаем браузер
    const browser = await puppeteer.launch({
        headless: !options.visible,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-infobars',
            '--ignore-certificate-errors',
            '--ignore-ssl-errors',
            '--disable-blink-features=AutomationControlled',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-dev-shm-usage',
            '--no-first-run',
            '--no-default-browser-check',
            '--disable-default-apps',
            '--disable-popup-blocking',
            '--disable-translate',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-field-trial-config',
            '--disable-back-forward-cache',
            '--disable-ipc-flooding-protection'
        ]
    });

    try {
        const page = await browser.newPage();

        // Настраиваем viewport и user agent
        await page.setViewport({ width: 1280, height: 800 });
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

        // Настраиваем дополнительные заголовки
        await page.setExtraHTTPHeaders({
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-User': '?1',
            'Sec-Fetch-Dest': 'document',
            'Cache-Control': 'max-age=0'
        });

        let allResults = [];

        if (categories) {
            // Парсим указанные категории
            const results = await processMultipleCategories(page, categories);
            allResults.push(...results);
        } else if (options.byCategory) {
            // Парсим группами по 3 категории
            const categoriesList = Object.keys(CLIENT_CATEGORIES);
            for (let i = 0; i < categoriesList.length; i += 3) {
                const categoryGroup = categoriesList.slice(i, i + 3);
                console.log(`\n🎯 === Начинаем обработку группы категорий: ${categoryGroup.join(', ')} ===`);
                const results = await processMultipleCategories(page, categoryGroup);
                allResults.push(...results);

                // Пауза между группами
                if (i + 3 < categoriesList.length) {
                    await humanDelay(5000, 8000, 'пауза между группами категорий');
                }
            }
        } else {
            // Парсим с популярными категориями по умолчанию
            const defaultCategories = ['Individuals', 'SMEs', 'Self-employed workers'];
            const results = await processMultipleCategories(page, defaultCategories);
            allResults.push(...results);
        }

        // Сохраняем результаты
        await saveResults(allResults);

        console.log(`\n🎉 Парсинг завершен! Всего найдено ${allResults.length} результатов.`);

    } catch (error) {
        console.error(`💥 Критическая ошибка: ${error.message}`);
        if (options.debug) {
            console.error(error.stack);
        }
    } finally {
        await browser.close();
    }
}

// Показываем справку если нужно
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log(`
🎯 CPA Quebec Parser с эмуляцией человеческого поведения (Puppeteer версия)

Использование:
  node quebec_parser_puppeteer.js [опции]

Опции:
  --visible              Запуск в видимом режиме
  --debug                Отладочный режим
  --slow                 Медленный режим (максимальные задержки)
  --fast                 Быстрый режим (минимальные задержки)
  --category <название>  Парсинг одной категории
  --categories <список>  Парсинг нескольких категорий
  --by-category          Парсинг всех категорий
  --help, -h             Показать эту справку

Доступные категории:
  ${Object.keys(CLIENT_CATEGORIES).join(', ')}

Примеры:
  node quebec_parser_puppeteer.js --visible
  node quebec_parser_puppeteer.js --visible --slow --category "Individuals"
  node quebec_parser_puppeteer.js --visible --categories "Individuals" "SMEs" "Start-ups"
  node quebec_parser_puppeteer.js --visible --by-category

Переменные окружения:
  TWOCAPTCHA_API_KEY     Ключ для 2captcha (приоритетный)
  ANTICAPTCHA_API_KEY    Ключ для Anti-Captcha (резервный)
`);
    process.exit(0);
}

// Запускаем парсер
if (require.main === module) {
    main().catch(error => {
        console.error(`💥 Фатальная ошибка: ${error.message}`);
        process.exit(1);
    });
}
