{"name": "proxy-agent", "version": "6.3.1", "description": "Maps proxy protocols to `http.Agent` implementations", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "engines": {"node": ">= 14"}, "repository": {"type": "git", "url": "https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/proxy-agent"}, "keywords": ["http", "https", "socks", "agent", "mapping", "proxy"], "author": "<PERSON> <<EMAIL>> (http://n8.io/)", "license": "MIT", "dependencies": {"agent-base": "^7.0.2", "debug": "^4.3.4", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.2", "lru-cache": "^7.14.1", "pac-proxy-agent": "^7.0.1", "proxy-from-env": "^1.1.0", "socks-proxy-agent": "^8.0.2"}, "devDependencies": {"@types/agent-base": "^4.2.0", "@types/debug": "^4.1.7", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/proxy-from-env": "^1.0.1", "@types/ws": "^8.5.4", "async-listen": "^3.0.0", "jest": "^29.5.0", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "ws": "^8.13.0", "proxy": "2.1.1", "tsconfig": "0.0.0"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}}